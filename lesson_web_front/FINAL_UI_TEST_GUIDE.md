# 课程编辑模态框最终测试指南

## 🎯 测试环境
- 开发服务器: http://localhost:3002/
- 测试页面: 课程管理 → 添加课程

## 📋 详细测试步骤

### 测试1: 课程名称placeholder垂直居中
**操作步骤**:
1. 点击"添加课程"按钮
2. 观察"课程名称"输入框中的placeholder文字

**预期结果**:
- ✅ placeholder "请输入课程名称" 在输入框中垂直居中显示
- ✅ 文字与书本图标在同一水平线上

### 测试2: 布局位置调整
**操作步骤**:
1. 观察表单整体布局

**预期结果**:
- ✅ 第一行: [课程名称] [课程类型] [每次消耗课时]
- ✅ 第二行: [上课教练(占16列)] [课程单价(占8列)]
- ✅ 第三行: [课程状态(占8列)]
- ✅ 课程单价与"每次消耗课时"上下对齐

### 测试3: 单选教练模式
**操作步骤**:
1. 在"上课教练"下拉框中选择一个教练（如：张雪风）
2. 观察界面变化

**预期结果**:
- ✅ "课程单价"字段自动填充教练的课时费
- ✅ 下方显示"教练课时费"卡片
- ✅ 卡片中显示选中教练的姓名和课时费
- ✅ 教练课时费输入框为禁用状态（不可编辑）

### 测试4: 多选教练模式
**操作步骤**:
1. 勾选"多教师教学"选项
2. 选择多个教练（如：张雪风、武文明）
3. 观察界面变化

**预期结果**:
- ✅ "课程单价"字段显示平均课时费
- ✅ 下方显示"各个教练课时费 (平均: ¥XX)"卡片
- ✅ 卡片标题中显示正确的平均值
- ✅ 卡片中显示所有选中教练的姓名和课时费
- ✅ 所有教练课时费输入框为可编辑状态

### 测试5: 多选模式课时费编辑
**操作步骤**:
1. 在多选教练模式下
2. 修改其中一个教练的课时费
3. 观察数值变化

**预期结果**:
- ✅ 修改课时费后，平均值自动重新计算
- ✅ 卡片标题中的平均值实时更新
- ✅ "课程单价"字段同步更新为新的平均值
- ✅ 计算结果保留两位小数

### 测试6: 模式切换
**操作步骤**:
1. 在多选模式下选择多个教练
2. 取消勾选"多教师教学"，切换到单选模式
3. 观察变化

**预期结果**:
- ✅ 只保留第一个选中的教练
- ✅ 卡片标题变为"教练课时费"
- ✅ 课时费更新为该教练的个人课时费
- ✅ 教练课时费输入框变为禁用状态

### 测试7: 清空选择
**操作步骤**:
1. 清空教练选择
2. 观察界面变化

**预期结果**:
- ✅ "课程单价"字段重置为0
- ✅ 教练课时费卡片消失
- ✅ 相关表单字段被清空

## 🔍 视觉检查要点

### 高度一致性
- [ ] 所有输入框高度统一为32px
- [ ] 课程名称输入框与课程类型选择框高度一致
- [ ] 课程单价输入框与上课教练选择框高度一致

### 对齐检查
- [ ] 课程单价与"每次消耗课时"完美上下对齐
- [ ] 表单标签左对齐
- [ ] 输入框内容垂直居中

### 间距检查
- [ ] 行间距合理，不会过于拥挤
- [ ] 列间距统一，使用16px间距
- [ ] 卡片与表单之间有适当间距

## 🐛 常见问题排查

### 问题1: placeholder没有垂直居中
**可能原因**: CSS样式没有正确应用
**检查方法**: 
1. 打开开发者工具
2. 检查`.ant-input-affix-wrapper`是否有`display: flex`和`align-items: center`

### 问题2: 课程单价没有自动填充
**可能原因**: 教练数据中缺少classFee字段
**检查方法**:
1. 查看Network面板中的API响应
2. 确认教练数据包含classFee字段

### 问题3: 平均值计算错误
**可能原因**: 数值类型转换问题
**检查方法**:
1. 打开控制台查看计算日志
2. 确认所有课时费都是有效数字

### 问题4: 卡片没有显示
**可能原因**: selectedCoaches数组为空
**检查方法**:
1. 在控制台输出selectedCoaches
2. 确认教练选择后数组正确更新

## ✅ 验收标准

### 基础功能 (必须全部通过)
- [ ] 课程名称placeholder垂直居中
- [ ] 课程单价与上课教练在同一行
- [ ] 课程单价与每次消耗课时上下对齐
- [ ] 单选教练时显示课时费卡片
- [ ] 多选教练时显示平均课时费
- [ ] 课时费修改时实时更新平均值和课程单价

### 用户体验 (重要)
- [ ] 界面布局合理，逻辑清晰
- [ ] 操作响应及时，无明显延迟
- [ ] 数据展示直观，易于理解
- [ ] 模式切换流畅，无异常

### 技术质量 (重要)
- [ ] 控制台无错误信息
- [ ] 数据计算准确
- [ ] 表单验证正常
- [ ] 样式渲染正确

## 📊 测试记录表

| 测试项目 | 状态 | 备注 |
|---------|------|------|
| placeholder垂直居中 | □ 通过 □ 失败 | |
| 布局位置调整 | □ 通过 □ 失败 | |
| 单选教练模式 | □ 通过 □ 失败 | |
| 多选教练模式 | □ 通过 □ 失败 | |
| 课时费编辑功能 | □ 通过 □ 失败 | |
| 模式切换 | □ 通过 □ 失败 | |
| 清空选择 | □ 通过 □ 失败 | |

**测试完成时间**: ___________
**测试人员**: ___________
**整体评价**: ___________
