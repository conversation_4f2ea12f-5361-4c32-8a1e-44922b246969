.course-edit-modal .ant-modal-content {
  position: relative;
}

.course-edit-modal .ant-spin-nested-loading {
  min-height: 300px; /* 确保加载状态下有足够的高度显示加载动画 */
}

.course-edit-modal .ant-spin-container {
  transition: all 0.3s;
}

.course-edit-modal .ant-select-disabled .ant-select-selection-item {
  color: rgba(0, 0, 0, 0.45);
}

.course-edit-modal .ant-input-number-disabled {
  color: rgba(0, 0, 0, 0.45);
}

.course-edit-modal .ant-form-item-label > label {
  font-weight: 500;
}

.course-edit-modal .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  color: #ff4d4f;
}

/* Select包装器 */
.select-wrapper {
  position: relative;
  width: 100%;
}

/* 教练选择下拉框样式修复 */
.course-edit-modal .ant-select {
  width: 100%;
}

.course-edit-modal .ant-select-selector {
  height: 32px !important;
  min-height: 32px !important;
}

/* 确保下拉菜单宽度与输入框一致 */
.course-edit-modal .ant-select-dropdown {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
}

/* 覆盖Ant Design的默认样式 */
.course-edit-modal .ant-select-dropdown-placement-bottomLeft {
  left: 0 !important;
}

/* 统一表单元素高度 */
.course-edit-modal .ant-input:not(.ant-input-textarea textarea),
.course-edit-modal .ant-input-number,
.course-edit-modal .ant-select-selector,
.course-edit-modal .ant-segmented {
  height: 32px !important;
  min-height: 32px !important;
}

/* 课程描述文本区域特殊样式 */
.course-edit-modal .ant-input-textarea textarea {
  height: auto !important;
  min-height: 100px !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
  vertical-align: top !important;
  display: flex !important;
  align-items: flex-start !important;
}

/* 课程描述placeholder样式 */
.course-edit-modal .ant-input-textarea textarea::placeholder {
  line-height: 1.5 !important;
  vertical-align: top !important;
  padding-top: 0 !important;
}

/* 确保课程名称输入框高度一致 */
.course-edit-modal .ant-input-affix-wrapper {
  height: 32px !important;
  min-height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

.course-edit-modal .ant-input-affix-wrapper .ant-input {
  height: 30px !important;
  line-height: 30px !important;
}

/* 确保placeholder垂直居中 */
.course-edit-modal .ant-input-affix-wrapper .ant-input::placeholder {
  line-height: 30px !important;
  vertical-align: middle !important;
}

.course-edit-modal .ant-input-number-input {
  height: 30px !important;
}

/* 状态选择器样式优化 */
.course-edit-modal .ant-segmented {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  border-radius: 6px !important;
}

.course-edit-modal .ant-segmented-item {
  height: 30px !important;
  line-height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 8px !important;
}

.course-edit-modal .ant-segmented-item-label {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 教练费用卡片样式 */
.coach-fee-card {
  margin-top: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.coach-fee-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.coach-fee-card .ant-card-body {
  padding: 16px;
}