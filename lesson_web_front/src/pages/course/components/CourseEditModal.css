.course-edit-modal .ant-modal-content {
  position: relative;
}

.course-edit-modal .ant-spin-nested-loading {
  min-height: 300px; /* 确保加载状态下有足够的高度显示加载动画 */
}

.course-edit-modal .ant-spin-container {
  transition: all 0.3s;
}

.course-edit-modal .ant-select-disabled .ant-select-selection-item {
  color: rgba(0, 0, 0, 0.45);
}

.course-edit-modal .ant-input-number-disabled {
  color: rgba(0, 0, 0, 0.45);
}

.course-edit-modal .ant-form-item-label > label {
  font-weight: 500;
}

.course-edit-modal .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  color: #ff4d4f;
}

/* Select包装器 */
.select-wrapper {
  position: relative;
  width: 100%;
}

/* 确保下拉菜单宽度与输入框一致 */
.ant-select-dropdown {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
}

/* 覆盖Ant Design的默认样式 */
.ant-select-dropdown-placement-bottomLeft {
  left: 0 !important;
}