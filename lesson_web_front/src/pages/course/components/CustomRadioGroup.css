/* 课程状态选择器样式 */
.course-status-segmented {
  width: 100% !important;
  height: 40px !important;
  background: #f5f5f5 !important;
  border-radius: 8px !important;
  padding: 2px !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.course-status-segmented .ant-segmented-item {
  border-radius: 6px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.course-status-segmented .ant-segmented-item:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.course-status-segmented .ant-segmented-item-selected {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-1px) !important;
}

/* 状态选项基础样式 */
.status-option {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 36px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.status-icon {
  margin-right: 6px !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.status-text {
  transition: all 0.3s ease !important;
}

/* 已发布状态样式 */
.status-published {
  color: #52c41a !important;
}

.course-status-segmented .ant-segmented-item:not(.ant-segmented-item-selected) .status-published {
  color: #73d13d !important;
}

.course-status-segmented .ant-segmented-item-selected .status-published {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.course-status-segmented .ant-segmented-item-selected .status-published::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 6px;
  z-index: -1;
}

/* 已暂停状态样式 */
.status-suspended {
  color: #fa8c16 !important;
}

.course-status-segmented .ant-segmented-item:not(.ant-segmented-item-selected) .status-suspended {
  color: #ffa940 !important;
}

.course-status-segmented .ant-segmented-item-selected .status-suspended {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.course-status-segmented .ant-segmented-item-selected .status-suspended::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
  border-radius: 6px;
  z-index: -1;
}

/* 已终止状态样式 */
.status-terminated {
  color: #ff4d4f !important;
}

.course-status-segmented .ant-segmented-item:not(.ant-segmented-item-selected) .status-terminated {
  color: #ff7875 !important;
}

.course-status-segmented .ant-segmented-item-selected .status-terminated {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.course-status-segmented .ant-segmented-item-selected .status-terminated::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border-radius: 6px;
  z-index: -1;
}

/* 悬停效果 */
.course-status-segmented .ant-segmented-item:hover .status-published .status-icon {
  transform: scale(1.1) rotate(5deg) !important;
}

.course-status-segmented .ant-segmented-item:hover .status-suspended .status-icon {
  transform: scale(1.1) rotate(-5deg) !important;
}

.course-status-segmented .ant-segmented-item:hover .status-terminated .status-icon {
  transform: scale(1.1) rotate(5deg) !important;
}

/* 选中状态的图标动画 */
.course-status-segmented .ant-segmented-item-selected .status-icon {
  transform: scale(1.05) !important;
}

/* 禁用状态 */
.course-status-segmented.ant-segmented-disabled .status-option {
  color: #bfbfbf !important;
  cursor: not-allowed !important;
}

.course-status-segmented.ant-segmented-disabled .ant-segmented-item:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .course-status-segmented {
    height: 36px !important;
  }
  
  .status-option {
    height: 32px !important;
    padding: 0 8px !important;
    font-size: 13px !important;
  }
  
  .status-icon {
    font-size: 14px !important;
    margin-right: 4px !important;
  }
}
