import React from 'react';
import { Segmented } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { CourseStatus } from '../types/course';

interface CustomRadioGroupProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
}

const CustomRadioGroup: React.FC<CustomRadioGroupProps> = ({ value, onChange, disabled = false }) => {
  const options = [
    {
      label: (
        <div style={{
          padding: '0 4px',
          display: 'flex',
          alignItems: 'center',
          height: '30px',
          fontSize: '14px'
        }}>
          <CheckCircleOutlined style={{ marginRight: 4, fontSize: '14px' }} />
          <span>已发布</span>
        </div>
      ),
      value: CourseStatus.PUBLISHED,
    },
    {
      label: (
        <div style={{
          padding: '0 4px',
          display: 'flex',
          alignItems: 'center',
          height: '30px',
          fontSize: '14px'
        }}>
          <ClockCircleOutlined style={{ marginRight: 4, fontSize: '14px' }} />
          <span>已暂停</span>
        </div>
      ),
      value: CourseStatus.SUSPENDED,
    },
    {
      label: (
        <div style={{
          padding: '0 4px',
          display: 'flex',
          alignItems: 'center',
          height: '30px',
          fontSize: '14px'
        }}>
          <CloseCircleOutlined style={{ marginRight: 4, fontSize: '14px' }} />
          <span>已终止</span>
        </div>
      ),
      value: CourseStatus.TERMINATED,
    },
  ];

  return (
    <Segmented
      options={options}
      value={value}
      onChange={onChange}
      disabled={disabled}
      block
      style={{
        width: '100%',
        height: '32px',
        display: 'flex',
        alignItems: 'center'
      }}
    />
  );
};

export default CustomRadioGroup;
