import React from 'react';
import { Segmented } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { CourseStatus } from '../types/course';

interface CustomRadioGroupProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
}

const CustomRadioGroup: React.FC<CustomRadioGroupProps> = ({ value, onChange, disabled = false }) => {
  const options = [
    {
      label: (
        <div style={{ padding: 4, display: 'flex', alignItems: 'center' }}>
          <CheckCircleOutlined style={{ marginRight: 6 }} />
          <div>已发布</div>
        </div>
      ),
      value: CourseStatus.PUBLISHED,
    },
    {
      label: (
        <div style={{ padding: 4, display: 'flex', alignItems: 'center' }}>
          <ClockCircleOutlined style={{ marginRight: 6 }} />
          <div>已暂停</div>
        </div>
      ),
      value: CourseStatus.SUSPENDED,
    },
    {
      label: (
        <div style={{ padding: 4, display: 'flex', alignItems: 'center' }}>
          <CloseCircleOutlined style={{ marginRight: 6 }} />
          <div>已终止</div>
        </div>
      ),
      value: CourseStatus.TERMINATED,
    },
  ];

  return (
    <Segmented
      options={options}
      value={value}
      onChange={onChange}
      disabled={disabled}
      block
      style={{ width: '100%' }}
    />
  );
};

export default CustomRadioGroup;
