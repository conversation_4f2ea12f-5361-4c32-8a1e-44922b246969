/* 确保下拉框宽度正确 */
.ant-select {
  width: 100% !important;
}

/* 确保下拉菜单宽度与输入框一致 */
.ant-select-dropdown {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
}

/* 覆盖Ant Design的默认样式 */
.ant-select-dropdown-placement-bottomLeft {
  left: 0 !important;
}

/* 特别处理角色下拉菜单 */
.role-select-dropdown {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  left: 0 !important;
}

/* Select包装器 */
.select-wrapper {
  position: relative;
  width: 100%;
}

/* 错误提示样式 */
.ant-form-item-explain-error {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}

/* 帮助文本样式 */
.ant-form-item-explain-success {
  font-size: 12px;
  color: #52c41a;
  margin-top: 4px;
}

/* 禁用密码输入框样式 */
.ant-input-password .ant-input[disabled] {
  background-color: #f5f5f5 !important;
  cursor: not-allowed !important;
  color: rgba(0, 0, 0, 0.65) !important;
  border-color: #d9d9d9 !important;
  opacity: 1 !important;
}

/* 只读字段样式 */
.readonly-field {
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.85);
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 32px;
  display: flex;
  align-items: center;
}
