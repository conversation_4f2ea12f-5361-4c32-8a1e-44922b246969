# 课程编辑模态框修复说明

## 修复的问题

### 1. 教练选择下拉框宽度问题
**问题描述**: 教练选择下拉框的下拉选项宽度与输入框不匹配

**修复方案**:
- 在 `CourseEditModal.css` 中添加了针对教练选择下拉框的样式修复
- 设置了 `popupMatchSelectWidth={true}` 和 `getPopupContainer` 属性
- 确保下拉菜单宽度与输入框一致

### 2. 表单元素高度统一问题
**问题描述**: 课程名称文本框的高度与课程类型文本框高度不一致，状态选择器高度也不统一

**修复方案**:
- 修改了 `CustomRadioGroup.tsx` 组件，统一了选项的高度和样式
- 在 `CourseEditModal.css` 中添加了统一表单元素高度的样式
- 特别针对带前缀图标的输入框（如课程名称）添加了高度修复
- 所有表单元素现在都有一致的32px高度

### 3. 布局位置调整
**问题描述**: 课程状态与每次消耗课时的位置需要交换

**修复方案**:
- 将第一行的"课程状态"移动到第三行第二列
- 将第三行的"每次消耗课时"移动到第一行第三列
- 保持了表单的逻辑性和用户体验

### 4. 课程单价自动填充功能
**问题描述**: 选择教练后，课程单价应该自动填入所选教练的课时费

**修复方案**:
- 修改了 `handleCoachChange` 函数，在设置教练课时费的同时也设置课程单价
- 支持单教师和多教师模式：
  - 单教师模式：直接使用该教练的课时费（classFee字段）
  - 多教师模式：使用所有教练课时费的平均值
- 在多教师切换到单教师时也会正确更新课程单价
- 确保从API返回的classFee字段正确映射到表单字段

## 修改的文件

1. **CourseEditModal.tsx**
   - 修改了教练选择下拉框的属性设置
   - 更新了 `handleCoachChange` 函数逻辑
   - 添加了课程单价自动填充功能

2. **CustomRadioGroup.tsx**
   - 优化了状态选项的样式和高度
   - 统一了图标和文字的大小和间距

3. **CourseEditModal.css**
   - 添加了教练选择下拉框的样式修复
   - 统一了所有表单元素的高度
   - 优化了状态选择器的样式
   - 添加了教练费用卡片的样式

## 测试建议

1. 打开课程管理页面
2. 点击"添加课程"或编辑现有课程
3. 验证以下功能：
   - 教练选择下拉框的宽度是否正确
   - 所有表单元素的高度是否一致
   - 选择教练后课程单价是否自动填充
   - 多教师模式切换是否正常工作

## 技术细节

- 使用了CSS的 `!important` 规则来确保样式优先级
- 通过 `getPopupContainer` 确保下拉框在正确的容器中渲染
- 使用了 `Math.round()` 来确保课时费计算的精度
- 保持了原有的表单验证和错误处理逻辑
