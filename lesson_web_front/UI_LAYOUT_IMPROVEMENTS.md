# 课程编辑模态框UI布局优化

## 🎯 优化目标

根据用户反馈，对课程编辑模态框进行以下UI和布局优化：

1. **修复课程名称文本框placeholder垂直居中显示**
2. **调整课程单价位置，与上课教练在同一行**
3. **优化多教师模式显示，移除独立的"平均教练课时费"字段**
4. **统一单选和多选教练的课时费显示样式**

## 🔧 技术实现

### 1. Placeholder垂直居中修复

**问题**: 课程名称输入框中的placeholder文字没有垂直居中显示

**解决方案**:
```css
/* 确保课程名称输入框高度一致 */
.course-edit-modal .ant-input-affix-wrapper {
  height: 32px !important;
  min-height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

.course-edit-modal .ant-input-affix-wrapper .ant-input {
  height: 30px !important;
  line-height: 30px !important;
}

/* 确保placeholder垂直居中 */
.course-edit-modal .ant-input-affix-wrapper .ant-input::placeholder {
  line-height: 30px !important;
  vertical-align: middle !important;
}
```

### 2. 布局调整

**调整前**:
```
第一行: [课程名称] [课程类型] [每次消耗课时]
第二行: [上课教练 (全宽)]
第三行: [教练课时费] [课程状态] [课程单价]
```

**调整后**:
```
第一行: [课程名称] [课程类型] [每次消耗课时]
第二行: [上课教练 (16列)] [课程单价 (8列)]
第三行: [课程状态 (8列)]
```

### 3. 教练课时费显示优化

**单选教练模式**:
- 显示"教练课时费"卡片
- 显示选中教练的课时费（只读）
- 课时费自动从API数据获取

**多选教练模式**:
- 显示"各个教练课时费 (平均: ¥XX)"卡片
- 标题中显示平均课时费
- 可以编辑每个教练的课时费
- 修改时自动重新计算平均值并更新课程单价

### 4. 数据流程优化

```typescript
// 选择教练时的处理逻辑
const handleCoachChange = async (value: number | number[]) => {
  // 单选模式
  if (values.length === 1) {
    const selectedCoach = selectedCoachList[0];
    const classFee = selectedCoach?.classFee || 0;
    
    form.setFieldsValue({
      coachFee: classFee,
      price: classFee,
      coachFees: { [selectedCoach.id]: classFee } // 统一显示格式
    });
  }
  
  // 多选模式
  if (isMultiTeacher) {
    const classFees = selectedCoachList.map(coach => coach.classFee || 0);
    const averageFee = classFees.reduce((sum, fee) => sum + fee, 0) / classFees.length;
    
    form.setFieldsValue({
      coachFee: Math.round(averageFee * 100) / 100,
      price: Math.round(averageFee * 100) / 100,
      coachFees: coachFees // 设置各教练课时费
    });
  }
};

// 教练课时费变化处理
const handleCoachFeeChange = (coachId: number, value: any) => {
  // 更新课时费并重新计算平均值
  const newCoachFees = { ...form.getFieldValue('coachFees'), [coachId]: value };
  const fees = Object.values(newCoachFees).filter(fee => typeof fee === 'number');
  const averageFee = fees.reduce((sum, fee) => sum + fee, 0) / fees.length;
  
  // 同时更新教练课时费和课程单价
  form.setFieldsValue({
    coachFee: parseFloat(averageFee.toFixed(2)),
    price: parseFloat(averageFee.toFixed(2))
  });
};
```

## 📋 用户体验改进

### 改进前的问题:
1. ❌ 课程名称placeholder显示不居中
2. ❌ 课程单价位置不合理，与教练选择分离
3. ❌ 多教师模式有独立的"平均教练课时费"字段，显得冗余
4. ❌ 单选和多选教练的课时费显示方式不一致

### 改进后的效果:
1. ✅ 课程名称placeholder完美垂直居中
2. ✅ 课程单价紧邻教练选择，逻辑更清晰
3. ✅ 多教师模式在标题中显示平均值，界面更简洁
4. ✅ 单选和多选统一使用卡片显示，体验一致

## 🧪 测试验证

访问 http://localhost:3002/ 进行测试：

### 基础UI测试
- [ ] 课程名称输入框placeholder垂直居中显示
- [ ] 课程单价与上课教练在同一行
- [ ] 课程单价与"每次消耗课时"上下对齐
- [ ] 所有表单元素高度统一

### 功能测试
- [ ] 单选教练时显示"教练课时费"卡片
- [ ] 多选教练时显示"各个教练课时费 (平均: ¥XX)"卡片
- [ ] 修改多选教练的课时费时，平均值和课程单价自动更新
- [ ] 从多选切换到单选时，显示正确的教练课时费

### 数据一致性测试
- [ ] 教练课时费与课程单价保持同步
- [ ] 平均值计算准确
- [ ] 模式切换时数据正确更新

## 📝 技术要点

1. **CSS Flexbox**: 使用flexbox确保输入框内容垂直居中
2. **动态标题**: 根据模式和数据动态生成卡片标题
3. **统一数据结构**: 单选和多选都使用coachFees对象存储数据
4. **实时计算**: 课时费变化时实时更新平均值和课程单价
5. **响应式布局**: 使用Ant Design的栅格系统确保布局适配

## 🎉 优化成果

通过这次优化，课程编辑模态框的用户体验得到了显著提升：
- 界面更加简洁统一
- 操作逻辑更加清晰
- 数据展示更加直观
- 响应速度更加快速
