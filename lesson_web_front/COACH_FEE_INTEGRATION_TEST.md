# 教练课时费集成测试指南

## 🎯 测试目标
验证课程编辑模态框能够正确从教练列表API获取课时费数据，并在选择教练时自动填充到相应的文本框中。

## 🔧 技术实现要点

### 1. API数据流
```
课程管理页面 → 调用 /api/coach/simple/list?campusId=1 → 获取教练列表(包含classFee)
     ↓
传递给CourseEditModal组件的cachedCoaches属性
     ↓
用户选择教练 → handleCoachChange函数 → 从cachedCoaches提取classFee → 填充表单
```

### 2. 关键代码修改
- **CoachSimple接口**: 添加了 `classFee?: number` 字段
- **handleCoachChange函数**: 直接从缓存数据获取课时费，无需额外API调用
- **性能优化**: 移除了不必要的 `coachAPI.getDetail()` 调用

## 📋 测试步骤

### 步骤1: 验证API数据
1. 打开浏览器开发者工具 (F12)
2. 访问 http://localhost:3002/
3. 在Network标签页中查找 `/api/coach/simple/list?campusId=1` 请求
4. 确认响应数据包含以下结构：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1000,
      "name": "杨大冬",
      "classFee": 100,
      "baseSalary": 3000,
      "performanceBonus": 0
    }
  ]
}
```

### 步骤2: 测试单教师模式
1. 点击"添加课程"按钮
2. 在"上课教练"下拉框中选择一个教练
3. **预期结果**:
   - "教练课时费"字段自动填充为该教练的classFee值
   - "课程单价"字段同时填充相同的值
   - 控制台输出: `单教师模式 - 选中教练: [教练姓名] 课时费: [数值]`

### 步骤3: 测试多教师模式
1. 勾选"多教师教学"选项
2. 选择多个教练（如：杨大冬、武文明）
3. **预期结果**:
   - "平均教练课时费"字段显示所有教练课时费的平均值
   - "课程单价"字段填充相同的平均值
   - 下方显示"各个教练课时费"卡片，列出每个教练的课时费
   - 控制台输出: `多教师模式 - 选中教练课时费: [数组] 平均课时费: [平均值]`

### 步骤4: 测试模式切换
1. 在多教师模式下选择多个教练
2. 取消勾选"多教师教学"，切换到单教师模式
3. **预期结果**:
   - 只保留第一个选中的教练
   - 课时费更新为该教练的个人课时费
   - 控制台输出: `切换到单教师模式 - 选中教练: [教练姓名] 课时费: [数值]`

### 步骤5: 测试清空选择
1. 清空教练选择
2. **预期结果**:
   - "教练课时费"字段重置为0
   - "课程单价"字段重置为0

## 🐛 常见问题排查

### 问题1: 课时费没有自动填充
**可能原因**:
- API返回的数据中缺少classFee字段
- CoachSimple接口定义不匹配
- cachedCoaches数据为空

**排查方法**:
1. 检查Network面板中的API响应
2. 在控制台输出 `console.log('cachedCoaches:', cachedCoaches)`
3. 确认教练数据中包含classFee字段

### 问题2: 多教师平均值计算错误
**可能原因**:
- 某些教练的classFee为null或undefined
- 数值类型转换问题

**排查方法**:
1. 检查控制台输出的课时费数组
2. 确认所有教练都有有效的classFee值

### 问题3: 模式切换时数据异常
**可能原因**:
- 表单字段清理不完整
- 状态更新时序问题

**排查方法**:
1. 检查form.setFieldsValue调用是否正确
2. 确认coachFees字段在单教师模式下被清空

## ✅ 验收标准

- [ ] API数据正确包含classFee字段
- [ ] 单教师选择时课时费自动填充
- [ ] 多教师选择时平均课时费计算正确
- [ ] 课程单价与教练课时费同步更新
- [ ] 模式切换时数据正确更新
- [ ] 清空选择时字段正确重置
- [ ] 控制台无错误信息
- [ ] 所有表单元素高度统一
- [ ] 布局位置符合要求（每次消耗课时在第一行，课程状态在第三行）

## 📝 测试记录

测试日期: ___________
测试人员: ___________
测试环境: http://localhost:3002/

| 测试项目 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| API数据获取 | 包含classFee字段 | | □ 通过 □ 失败 |
| 单教师课时费填充 | 自动填充正确数值 | | □ 通过 □ 失败 |
| 多教师平均值计算 | 计算结果正确 | | □ 通过 □ 失败 |
| 课程单价同步 | 与课时费一致 | | □ 通过 □ 失败 |
| 模式切换 | 数据正确更新 | | □ 通过 □ 失败 |
| 清空选择 | 字段重置为0 | | □ 通过 □ 失败 |
| 布局调整 | 位置符合要求 | | □ 通过 □ 失败 |
| 高度统一 | 所有元素32px | | □ 通过 □ 失败 |

备注: ___________________________________________
